<template>
	<view class="tc-player-container">
		<view :id="playerId" class="tc-player"></view>
	</view>
</template>

<script>
export default {
	name: 'TcPlayer',
	props: {
		// 播放器ID
		playerId: {
			type: String,
			default: 'tc-player'
		},
		// 直播流地址
		src: {
			type: String,
			required: true
		},
		// 封面图片
		poster: {
			type: String,
			default: ''
		},
		// 是否自动播放
		autoplay: {
			type: Boolean,
			default: true
		},
		// 是否显示控制栏
		controls: {
			type: Boolean,
			default: false
		},
		// 是否循环播放
		loop: {
			type: Boolean,
			default: true
		},
		// 播放器宽度
		width: {
			type: [String, Number],
			default: '100%'
		},
		// 播放器高度
		height: {
			type: [String, Number],
			default: '100%'
		}
	},
	data() {
		return {
			player: null
		}
	},
	mounted() {
		this.$nextTick(() => {
			this.initPlayer();
		});
	},
	beforeDestroy() {
		if (this.player) {
			this.player.destroy();
		}
	},
	methods: {
		initPlayer() {
			// 检查TCPlayer是否已加载
			if (typeof TCPlayer === 'undefined') {
				console.error('TCPlayer SDK未加载，请检查script标签是否正确引入');
				return;
			}

			const options = {
				// 播放器容器ID
				containerId: this.playerId,
				// 播放源配置
				sources: [{
					src: this.src,
					type: this.getVideoType(this.src)
				}],
				// 播放器配置
				autoplay: this.autoplay,
				poster: this.poster,
				controls: this.controls,
				loop: this.loop,
				width: this.width,
				height: this.height,
				// 直播配置
				live: true,
				// 播放器皮肤
				skinName: 'default',
				// 插件配置
				plugins: {
					// 启用HLS插件用于直播流
					HlsJSPlugin: {},
					// 启用FLV插件用于直播流
					FlvJSPlugin: {}
				},
				// 其他配置
				playbackRates: [0.5, 1, 1.25, 1.5, 2], // 播放速度选项
				bigPlayButton: false, // 不显示大播放按钮
				errorDisplay: true, // 显示错误信息
				language: 'zh-cn' // 中文界面
			};

			try {
				// 创建播放器实例
				this.player = new TCPlayer(options);
				
				// 绑定事件
				this.bindEvents();
				
				console.log('TCPlayer初始化成功');
			} catch (error) {
				console.error('TCPlayer初始化失败:', error);
			}
		},
		
		bindEvents() {
			if (!this.player) return;
			
			// 播放事件
			this.player.on('play', () => {
				this.$emit('play');
			});
			
			// 暂停事件
			this.player.on('pause', () => {
				this.$emit('pause');
			});
			
			// 错误事件
			this.player.on('error', (error) => {
				console.error('播放器错误:', error);
				this.$emit('error', error);
			});
			
			// 加载完成事件
			this.player.on('loadeddata', () => {
				this.$emit('loadeddata');
			});
			
			// 点击事件
			this.player.on('click', () => {
				this.$emit('click');
			});
		},
		
		// 根据URL判断视频类型
		getVideoType(url) {
			if (url.includes('.m3u8')) {
				return 'application/x-mpegURL'; // HLS
			} else if (url.includes('.flv')) {
				return 'video/x-flv'; // FLV
			} else if (url.includes('.mp4')) {
				return 'video/mp4'; // MP4
			} else {
				return 'application/x-mpegURL'; // 默认使用HLS
			}
		},
		
		// 播放
		play() {
			if (this.player) {
				this.player.play();
			}
		},
		
		// 暂停
		pause() {
			if (this.player) {
				this.player.pause();
			}
		},
		
		// 切换播放/暂停
		toggle() {
			if (this.player) {
				if (this.player.paused()) {
					this.player.play();
				} else {
					this.player.pause();
				}
			}
		},
		
		// 设置音量
		setVolume(volume) {
			if (this.player) {
				this.player.volume(volume);
			}
		},
		
		// 切换静音
		toggleMute() {
			if (this.player) {
				this.player.muted(!this.player.muted());
			}
		},
		
		// 更新播放源
		updateSrc(newSrc) {
			if (this.player) {
				this.player.src({
					src: newSrc,
					type: this.getVideoType(newSrc)
				});
			}
		}
	},
	watch: {
		// 监听src变化
		src(newSrc) {
			if (newSrc && this.player) {
				this.updateSrc(newSrc);
			}
		}
	}
}
</script>

<style scoped>
.tc-player-container {
	width: 100%;
	height: 100%;
	position: relative;
}

.tc-player {
	width: 100%;
	height: 100%;
}

/* 隐藏TCPlayer默认的控制栏样式 */
.tc-player-container >>> .vjs-control-bar {
	display: none !important;
}

/* 隐藏大播放按钮 */
.tc-player-container >>> .vjs-big-play-button {
	display: none !important;
}

/* 确保播放器填满容器 */
.tc-player-container >>> .video-js {
	width: 100% !important;
	height: 100% !important;
}

.tc-player-container >>> .vjs-tech {
	width: 100% !important;
	height: 100% !important;
	object-fit: contain;
}
</style>
