# TCPlayer 腾讯云播放器组件

这是一个基于腾讯云TCPlayer的Vue组件，专门用于直播流播放。

## 功能特性

- ✅ 支持多种直播流格式（HLS、FLV、MP4等）
- ✅ 自动播放和循环播放
- ✅ 自定义封面图片
- ✅ 事件监听（播放、暂停、错误等）
- ✅ 响应式设计
- ✅ 可配置的播放器选项

## 安装和配置

### 1. 引入TCPlayer SDK

在 `index.html` 中添加TCPlayer SDK：

```html
<script src="https://web.sdk.qcloud.com/player/tcplayer/release/v4.7.2/tcplayer.v4.7.2.min.js"></script>
```

### 2. 使用组件

```vue
<template>
  <tc-player
    class="video"
    player-id="live-player"
    :src="liveStreamUrl"
    :poster="posterUrl"
    :autoplay="true"
    :controls="false"
    @click="handleClick"
    @play="onPlay"
    @pause="onPause"
    @error="onError"
  ></tc-player>
</template>

<script>
import tcPlayer from '@/components/tc-player/tc-player.vue';

export default {
  components: { tcPlayer },
  data() {
    return {
      // HLS直播流示例
      liveStreamUrl: "https://example.com/live/stream.m3u8",
      // FLV直播流示例
      // liveStreamUrl: "https://example.com/live/stream.flv",
      posterUrl: require("@/static/images/poster.png")
    }
  },
  methods: {
    handleClick() {
      console.log('播放器被点击');
    },
    onPlay() {
      console.log('开始播放');
    },
    onPause() {
      console.log('暂停播放');
    },
    onError(error) {
      console.error('播放错误:', error);
    }
  }
}
</script>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| playerId | String | 'tc-player' | 播放器唯一ID |
| src | String | - | 直播流地址（必填） |
| poster | String | '' | 封面图片地址 |
| autoplay | Boolean | true | 是否自动播放 |
| controls | Boolean | false | 是否显示控制栏 |
| loop | Boolean | true | 是否循环播放 |
| width | String/Number | '100%' | 播放器宽度 |
| height | String/Number | '100%' | 播放器高度 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| play | 开始播放时触发 | - |
| pause | 暂停播放时触发 | - |
| error | 播放错误时触发 | error对象 |
| loadeddata | 数据加载完成时触发 | - |
| click | 点击播放器时触发 | - |

## 方法说明

通过ref可以调用以下方法：

```javascript
// 获取播放器实例
this.$refs.player.play();        // 播放
this.$refs.player.pause();       // 暂停
this.$refs.player.toggle();      // 切换播放/暂停
this.$refs.player.setVolume(0.5); // 设置音量(0-1)
this.$refs.player.toggleMute();  // 切换静音
this.$refs.player.updateSrc(newUrl); // 更新播放源
```

## 支持的直播流格式

### HLS (.m3u8)
```javascript
liveStreamUrl: "https://example.com/live/stream.m3u8"
```

### FLV (.flv)
```javascript
liveStreamUrl: "https://example.com/live/stream.flv"
```

### MP4 (.mp4)
```javascript
liveStreamUrl: "https://example.com/video/stream.mp4"
```

## 注意事项

1. **SDK依赖**: 确保在使用组件前已正确引入TCPlayer SDK
2. **HTTPS要求**: 在生产环境中，直播流地址建议使用HTTPS协议
3. **跨域问题**: 确保直播流服务器支持跨域访问
4. **移动端适配**: 组件已针对移动端进行优化，支持触摸操作
5. **性能优化**: 组件会在销毁时自动清理播放器实例，避免内存泄漏

## 常见问题

### Q: 播放器无法加载？
A: 检查TCPlayer SDK是否正确引入，查看浏览器控制台是否有错误信息。

### Q: 直播流无法播放？
A: 检查直播流地址是否正确，确认服务器支持跨域访问。

### Q: 在某些设备上无法自动播放？
A: 部分移动设备和浏览器限制自动播放，需要用户手动触发播放。

## 更新日志

- v1.0.0: 初始版本，支持基本的直播流播放功能
