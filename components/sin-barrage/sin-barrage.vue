<template>
	<view class="barrage" :style="{bottom:bottom + 'rpx', left: left + 'rpx'}">
		<uni-transition :mode-class="['fade', 'slide-left']" :show="true" v-for="(item, index) in barrageList" :key="item.id">
			<view class="gift_box flex">
				<image class="avatar" :src="item.image" mode=""></image>
				<view class="user flex flex-column">
					<text>萧炎</text>
					<text class="small">送出</text>
				</view>
				<image class="gift" src="https://rdimg.rundejy.com/web/runde_admin/icon_鼓掌-1.png" mode=""></image>
				<view class="count">x<text>1</text></view>
			</view>
		</uni-transition>
	</view>
</template>

<script>
	export default {
		name: "sinBarrage",
		props: {
			list: {
				type: Array || Object,
				default () {
					return {}
				}
			},
			rows: {
				type: Number,
				default: 3
			},
			color: {
				type: String,
				default: '#FFFFFF'
			},
			background: {
				type: String,
				default: '#000000'
			},
			opacity: {
				type: Number,
				default: 0.7
			},
			left: {
				type: Number,
				default: 35
			},
			bottom: {
				type: Number,
				default: 120
			},
			msec: {
				type: Number,
				default: 2000
			}
		},
		data() {
			return {
				barrageList: [],
				timer: null,
				show: false,
			}
		},
		created() {
			if (this.list.length == 0) return;
			if(this.timer) clearInterval(this.timer);
			if (this.list.length == 1) {
				this.barrageList.push(this.list[0]);
				setTimeout(() => this.barrageList = [], this.msec);
				return;
			}
			this.timer = setInterval(() => {
				if (this.barrageList.length < this.rows) {
					this.barrageList.push(this.list[0])
					this.list.splice(0, 1)
				} else {
					let objAFristItem = this.barrageList[0]
					this.barrageList.splice(objAFristItem, 1)
					this.list.push(objAFristItem)
					let objBFirstItem = this.list[0]
					this.list.splice(objBFirstItem, 1)
					this.barrageList.push(objBFirstItem)
				}
			}, this.msec)
		},
	}
</script>

<style scoped lang="scss">

	.barrage {
		position: absolute;
		z-index: 999;
		left: 30rpx;
		
		
	}
	
	.gift_box {
		background: linear-gradient(to right, #F660AA, rgba(242,95,168,0));
		border-top-left-radius: 100px;
		border-bottom-left-radius: 100px;
		padding: 4rpx 10rpx 4rpx 4rpx;
		margin-top: 20rpx;
		width: 140px;
		
		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 100%;
		}
		
		.user {
			margin: 0 5px;
			text {
				font-size: 28rpx;
				color: #fff;
				align-self: flex-start;
			}
			.small {
				font-size: 22rpx;
			}
		}
		
		.gift {
			width: 120rpx;
			height: 120rpx;
			position: absolute;
			right: -30rpx;
		}
		
		.count {
			position: absolute;
			font-size: 30rpx;
			color: #fd9c05;
			margin-left: 10rpx;
			right: -60rpx;
			bottom: 0;
			text {
				font-size: 50rpx;
				color: #fd9c05;
			}
		}
	}
</style>
