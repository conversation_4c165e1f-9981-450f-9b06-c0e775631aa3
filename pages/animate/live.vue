<template>
	<view class="live">
		<video 
			class="video" 
			object-fit="contain" 
			:loop="true"
			:autoplay="true"
			:controls="false"
			:show-center-play-btn="false"
			src="https://video1.guohuamall.cn/record/live/6eafffe9-943f-4f93-b78a-d638065ec27c_67a73ec2-fdd6-4fda-804b-260aaa12bf5f_75301_camera/2025-06-28-10-44-35_2025-06-28-11-33-48.mp4"
			poster="@/static/video/1-poster.png"
			@click="hideCoverStatus = false"
		></video> 
		
		<cover-view class="content" :class="{hide: hideCoverStatus}">
			<!-- 用户信息 -->
			<live-header></live-header>
			
			<!-- 礼物、人物进场动画 -->
			<live-gift ref="liveGift"></live-gift>
			
			<!-- 消息 -->
			<live-msg ref="liveMsg"></live-msg>
			
			<!-- 尾巴 -->
			<live-footer @clear="hideCoverStatus = true" @sendLiveMsg="sendLiveMsg" @welcomeUser="welcomeUser" @sendGift="sendGift"></live-footer>
		</cover-view>
	</view>
</template>

<script>
	import liveHeader from './components/liveHeader.vue';
	import liveGift from './components/liveGift.vue';
	import liveMsg from './components/liveMsg.vue';
	import liveFooter from './components/liveFooter.vue';
	export default {
		components: { liveHeader, liveGift, liveMsg, liveFooter },
		data() {
			return {
				hideCoverStatus: false
			}
		},
		onLoad() {
			this.$nextTick(() => {
				// this.$refs.liveGift.sendGift();
				this.$refs.liveGift.welcomeUser();
			}) 
		},
		methods: {
			welcomeUser() {
				this.$refs.liveGift.welcomeUser();
			},
			
			sendGift(data) {
				this.$refs.liveGift.sendGift(data.index);
			},
			
			sendLiveMsg(data) {
				this.$refs.liveMsg.sendLiveMsg(data.msg);
			},
		}
	}
</script>

<style scoped lang="scss">
	.live {
		position: relative;
		height: 100vh;
		overflow: hidden;
	}
	
	.video {
		height: 100vh;
		width: 100%;
	}
	
	.content {
		// background: rgba(0, 0, 0, 0.6);
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
		transition: left 0.3s ease;
	}
	
	.hide {
		left: 100vw;
	}
</style>
