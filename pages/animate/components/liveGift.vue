<template>
	<view class="animation-area">
		<!-- 送礼物动画 -->
		<sin-barrage
			ref="sinbarrage"
			:rows="2"
			:list="giftList" 
			:bottom="welcome ? 60 : 0" 
			:left="30" 
			:opacity="0.5" 
			:msec="msec"
		 ></sin-barrage>
		
		<!-- 用户入场动画 -->
		<view class="welcome" v-if="welcome">
			欢迎<text class="username">大魔王</text>加入直播间
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				giftList: [
					{ id: 1, text: '第一条消息', image: 'https://img0.baidu.com/it/u=4105778329,1297102594&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1710522000&t=7fa692d29ff484bda51d1f85c4fe6262'},
					{ id: 2, text: '哇哈哈', image: 'https://img1.baidu.com/it/u=2985396150,1670050748&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1710522000&t=394243c2e302e0f329ac7a6206defb5d'},
					{ id: 3, text: 'AD钙', image: 'https://img1.baidu.com/it/u=3595003471,3445740740&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1710522000&t=807e95c880870da88b19bf7c55850812'},
					{ id: 4, text: '哇哈哈矿泉水', image: 'https://img2.baidu.com/it/u=2909427933,1285872418&fm=253&app=138&size=w931&n=0&f=JPEG&fmt=auto?sec=1710522000&t=fa2524753bf8160264028153e82e8efd'}
				],
				bottom: 200,
				msec: 3000,
				
				welcome: false,
				allList: [ //总的礼物列表
					{
						giftImage: 'https://rdimg.rundejy.com/web/runde_admin/icon_奖杯-1.png',
						liveGiftName: '奖杯'
					},
					{
						giftImage: 'https://rdimg.rundejy.com/web/runde_admin/icon_鼓掌-1.png',
						liveGiftName: '鼓掌'
					}
				]
			}
		},
		methods: {
			sendGift(index) {
				
			},
			
			welcomeUser() {
				if (this.welcome) return;
				this.welcome = true;
				setTimeout(() => this.welcome = false, 2000);
			}
		}
	}
</script>

<style scoped lang="scss">
	
	.animation-area {
		position: absolute;
		bottom: 550rpx;
		left: 0;
		right: 0;
		height: 200rpx;
	}

	.welcome {
		background: linear-gradient(to right, rgba(173,46,253,0.8), rgba(173,46,253,0));
		color: #fff;
		font-size: 14px;
		padding: 5px 10px;
		border-top-left-radius: 100px;
		border-bottom-left-radius: 100px;
		animation: welcome 2s forwards;
		
		position: absolute;
		top: 80px;
		width: 260px;
		right: -300px;
		
		.username {
			font-size: 14px;
			padding: 0 2px;
			font-weight: bold;
		}
	}
	
	.send {
		border: 1px solid red;
		background: #ff2b2b;
		color: #fff;
		text-align: center;
	}

	@keyframes welcome {
		0% {
			right: -300px;
		}
	
		20% {
			right: 75px
		}
		
		30% {
			right: 78px
		}
		
		60% {
			right: 80px
		}
		
		80% {
			right: 80px
		}
		
		90% {
			right: 80px
		}
	
		100% {
			right: 605px
		}
	}
</style>