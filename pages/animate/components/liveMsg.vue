<template>
	<view class="message">
		<scroll-view class="msg-scroll" :scroll-top="scrollTop" :class="{mask: msgMask}" @scroll="scrollEvent" :show-scrollbar="false" :scroll-with-animation="true" scroll-y>
			<view class="msg flex flex-column" id="msg-area">
				<view class="msg-item system">
					xxx提倡绿色直播，严禁未成年人直播或打赏，严禁涉政、涉恐、涉黄、聚集闹事、返现等内容，平台将会24小时巡查。请勿参与直播间非官方奖励活动/游戏，切勿私下交易，以防受骗。
				</view>
				<view class="msg-item">
					<image src="@/static/gif/41_1.gif" mode="heightFix"></image>
					<image src="@/static/gif/30.gif" mode="heightFix"></image>
					<text class="username">海波东：</text>
					萧炎这小子..
				</view>
				<view class="msg-item">
					<image src="@/static/gif/40_1.gif" mode="heightFix"></image>
					<image src="@/static/gif/32.gif" mode="heightFix"></image>
					<text class="username">小医仙：</text>
					哼...
				</view>
				<view class="msg-item">
					<image src="@/static/gif/40_1.gif" mode="heightFix"></image>
					<image src="@/static/gif/36.gif" mode="heightFix"></image>
					<text class="username">薰儿：</text>
					哼哼...
				</view>
				<view class="msg-item" v-for="(item, index) in messageList">
					<image src="@/static/gif/32.gif" mode="heightFix"></image>
					<image src="@/static/gif/22.gif" mode="heightFix"></image>
					<text class="username">{{ item.nickname }}：</text>
					{{ item.message }}
				</view>
				
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				msgMask: null,
				messageList: [],
				scrollTop: 0
			}
		},
		
		methods: {
			scrollEvent(e) {
				this.msgMask = e.detail.scrollTop > 0;
			},
			
			sendLiveMsg(message) {
				this.messageList.push({
					nickname: '陌生网友',
					message
				});
				this.scrollToLower();
			},
			
			scrollToLower() {
			    setTimeout(() => {
			    	const query = uni.createSelectorQuery().in(this);
			    	query.select('#msg-area').boundingClientRect(data => {
			    		this.scrollTop = data.height;
			    	}).exec();
			    }, 50);
			}
		}
	}
</script>

<style scoped lang="scss">
	.message {
		position: absolute;
		bottom: 120rpx;
		left: 20rpx;
		right: 20rpx;
		min-width: 750rpx;

		.msg-scroll {
			height: 400rpx;

			&.mask {
				-webkit-mask: -webkit-gradient(linear,left 30%,left top,from(#000),to(transparent));
			}

			.msg-item {
				max-width: 80%;
				background: var(--live-background);
				margin-bottom: 10rpx;
				color: #fff;
				font-size: 28rpx;
				padding: 15rpx 20rpx;
				border-radius: 20rpx;
				white-space: pre-wrap;
				align-self: flex-start;
				transition: opacity 0.3s ease;
				
				image {
					height: 36rpx;
					margin-right: 5rpx;
					vertical-align: middle;
				}

				.username {
					font-weight: 500;
					color: #77c4bc;
				}
				
				&.system {
					color: #77c4bc;
				}
			}
		}
	}
</style>