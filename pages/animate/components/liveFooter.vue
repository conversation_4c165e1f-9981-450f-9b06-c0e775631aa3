<template>
	<view>
		<view class="footer flex">
			<view class="input" @click="sendMsg">说点什么...</view>
			<view class="tools flex">
				<view class="tool-item cart" @click="welcomeUser">
					<uni-icons type="cart-filled" size="22" color="#fff"></uni-icons>
				</view>
				<view class="tool-item gift" @click="openGift">
					<uni-icons type="gift-filled" size="22" color="#fff"></uni-icons>
				</view>
				<likeButton :throttle="100" :large="2" :duration="3000" :imgWidth="90" :imgHeight="90" :site="[105, 100]" :showImgs="likeImgs">
					<view class="tool-item like">
						<uni-icons type="hand-up-filled" size="22" color="#fff"></uni-icons>
					</view>
				</likeButton>
				<view class="tool-item" @click="clear">
					<uni-icons type="closeempty" size="22" color="#fff"></uni-icons>
				</view>
			</view>
		</view>
		
		<uni-popup ref="giftPopup" :mask-click="false">
			<view class="gift-main flex">
				<view class="gift-item flex flex-column" @click="sendGift(0)">
					<image src="https://rdimg.rundejy.com/web/runde_admin/icon_奖杯-1.png" mode=""></image>
					<text>奖杯</text>
				</view>
				<view class="gift-item flex flex-column" @click="sendGift(1)">
					<image src="https://rdimg.rundejy.com/web/runde_admin/icon_鼓掌-1.png" mode=""></image>
					<text>鼓掌</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import likeButton from '@/components/like-button/like-button.vue'
	export default {
		components: { likeButton },
		data() {
			return {
				likeImgs: []
			}
		},
		mounted() {
			this.likeImgs = [
				require('@/static/like/bg1.png'),
				require('@/static/like/bg2.png'),
				require('@/static/like/bg3.png'),
				require('@/static/like/bg4.png'),
				require('@/static/like/bg5.png'),
				require('@/static/like/bg6.png'),
			];
			
			// 模拟效果
			// setInterval(() => {
			// 	this.openGift();
			// 	this.welcomeUser();
			// }, 300)
		},
		methods: {
			openGift() {
				this.$emit('sendGift', { index: 1 });
				// this.$refs.giftPopup.open('bottom')
			},
			
			sendGift(index) {
				this.$refs.giftPopup.close();
				this.$emit('sendGift', { index });
			},
			
			welcomeUser() {
				this.$emit('welcomeUser');
			},
			
			sendMsg() {
				this.$emit('sendLiveMsg', { msg: '模拟一条评论消息～' });
			},
			
			clear() {
				this.$emit('clear');
			}
		}
	}
</script>

<style scoped lang="scss">
	.footer {
		position: absolute;
		bottom: 0;
		padding: 20rpx;
		height: 100rpx;
		width: calc(100vw - 40rpx);
		
		.input {
			background: var(--live-background);
			color: #ccc;
			border-radius: 100rpx;
			font-size: 28rpx;
			height: 80rpx;
			line-height: 80rpx;
			padding-left: 20rpx;
			flex: 1;
		}
		
		.tools {
			justify-content: space-around;
			
			.tool-item {
				width: 80rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 100%;
				margin-left: 20rpx;
				color: #fff;
				background: var(--live-background);
				
				&.cart {
					background: linear-gradient(to right, #E7B211, #E98824)!important;
				}
				
				&.gift {
					background: linear-gradient(to right, #E77DD2, #EA35AA)!important;
				}
				
				&.like {
					background: linear-gradient(to bottom right, #FF4857, #FF2C80)!important;
				}
			}
		}
	}
	
	.gift-main {
		height: 600rpx;
		background: rgba(0, 0, 0, 0.8);
		align-items: flex-start;
		flex-wrap: wrap;
		padding: 30rpx;
		justify-content: flex-start;
		
		.gift-item {
			width: 120rpx;
			border: 2rpx solid #000;
			margin-right: 16rpx;
			
			&:nth-child(5n) {
				margin-right: 0;
			}
			image {
				width: 100rpx;
				height: 100rpx;
			}
			text {
				font-size: 24rpx;
				color: #fff;
			}
		}
	}
</style>