<template>
	<view class="header">
		<view class="top flex">
			<view class="user flex">
				<image class="avatar" src="@/static/video/1-poster.png" mode=""></image>
				<view class="userinfo flex flex-column">
					<text class="username">斗破苍穹官方</text>
					<text class="desc">9999本场点赞</text>
				</view>
				<view class="follow flex">
					<uni-icons type="plusempty" color="" size="12"></uni-icons>
					<text>关注</text>
				</view>
			</view>
			<view class="onlineuser flex">
				<view class="avatar flex flex-column">
					<image src="@/static/avatar/1.jpeg" mode=""></image>
					<text>1</text>
				</view>
				<view class="avatar flex flex-column">
					<image src="@/static/avatar/2.jpeg" mode=""></image>
				</view>
				<view class="avatar flex flex-column">
					<image src="@/static/avatar/3.jpeg" mode=""></image>
				</view>
			</view>
			<!-- <uni-icons class="close" type="closeempty" size="22" color="#fff"></uni-icons> -->
		</view>
		
		<view class="title-list flex">
			<view class="title-item flex">
				<uni-icons type="cart" size="16" color="#ffab01"></uni-icons>
				<text class="text">动漫热榜第1名</text>
			</view>
			<view class="title-item flex">
				<uni-icons type="flag" size="16" color="#ffab01"></uni-icons>
				<text class="text">小时榜</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		
	}
</script>

<style scoped lang="scss">
	.header {
		position: absolute;
		top: 20rpx;
		left: 20rpx;
		right: 0;
		min-width: 750rpx;
		
		.top {
			width: 100%;
			justify-content: space-between;
		}
		
		.close {
			margin-right: 20rpx;
		}
		
		.user {
			background: rgba(0, 0, 0, 0.3);
			border-radius: 100px;
			padding: 6rpx;
			
			.avatar {
				height: 60rpx;
				width: 60rpx;
				border-radius: 100%;
			}
			
			.userinfo {
				padding: 0 10rpx;
				.username {
					color: #fff;
					font-size: 26rpx;
					font-weight: 500;
					align-self: flex-start;
				}
				.desc {
					color: #ccc;
					font-size: 22rpx;
					align-self: flex-start;
				}
			}
			
			.follow {
				font-size: 22rpx;
				background: linear-gradient(to right, #FF4656, #FE2B8C);
				color: #fff;
				width: 90rpx;
				border-radius: 100rpx;
				height: 60rpx;
				line-height: 60rpx;
				justify-content: center;
			}
		}
		
		.onlineuser {
			.avatar {
				position: relative;
				
				text {
					width: 72rpx;
					height: 30rpx;
					line-height: 30rpx;
					font-size: 16rpx;
					text-align: center;
					position: absolute;
					left: 2rpx;
					bottom: 0px;
					font-weight: 500;
					color: #fff;
				}
				
				image {
					width: 74rpx;
					height: 74rpx;
					border-radius: 100%;
					margin-right: 20rpx;
				}
				
				&:first-child {
					
					&::after {
						content: " ";
						width: 74rpx;
						height: 74rpx;
						background-image: url(@/static/icons/shouhu.webp);
						background-size: 74rpx 74rpx;
						background-repeat: no-repeat;
						background-position: center center;
						display: block;
						position: absolute;
						left: 0px;
						top: 0px;
						z-index: 1;
					}
					
					text {
						border: none;
						background-image: url(https://vr0.6rooms.com/tao/i/n7/2449060832f8a39fe6772f87de1bef9d.png);
						background-size: 100% 100%;
					}
				}
			}
		}
		
		.title-list {
			margin-top: 20rpx;
			.title-item {
				background: var(--live-background);
				border-radius: 100rpx;
				margin-right: 20rpx;
				padding: 5rpx 15rpx;
				.text {
					font-size: 24rpx;
					color: #fff;
					margin-left: 2rpx;
				}
			}
		}
	}
</style>