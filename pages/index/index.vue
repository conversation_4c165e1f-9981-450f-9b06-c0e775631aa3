<template>
	<sin-barrage
		ref="sinbarrage"
		:list="bobaoList" 
		:bottom="bottom" 
		:left="left" 
		:opacity="0.5" :rows="2"
		:msec="msec"
	 ></sin-barrage>
</template>

<script>
	export default {
		data() {
			return {
				bobaoList: [
					{ id: 1, text: '第一条消息', image: 'https://img0.baidu.com/it/u=4105778329,1297102594&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1710522000&t=7fa692d29ff484bda51d1f85c4fe6262'},
					{ id: 2, text: '哇哈哈', image: 'https://img1.baidu.com/it/u=2985396150,1670050748&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1710522000&t=394243c2e302e0f329ac7a6206defb5d'},
					{ id: 3, text: 'AD钙', image: 'https://img1.baidu.com/it/u=3595003471,3445740740&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1710522000&t=807e95c880870da88b19bf7c55850812'},
					{ id: 4, text: '哇哈哈矿泉水', image: 'https://img2.baidu.com/it/u=2909427933,1285872418&fm=253&app=138&size=w931&n=0&f=JPEG&fmt=auto?sec=1710522000&t=fa2524753bf8160264028153e82e8efd'}
				],
				bottom: -100,
				left: 30,
				msec: 3000
			}
		}
	}
</script>

<style scoped lang="scss">
	
</style>